"use strict"
/*************************
    ZenzefiCalcLogs Manager
    Coded by <PERSON><PERSON>
**************************/

import DatabaseManager from './DatabaseManager.js';

class ZenzefiLogsManager extends DatabaseManager {
    constructor() {
        super();
        this.tableName = 'ZenzefiCalcLogs';
        this.statusTypes = {
            PENDING: 'pending',
            PROCESSING: 'processing',
            COMPLETED: 'completed',
            FAILED: 'failed',
            TIMEOUT: 'timeout'
        };
    }

    // Yeni ZenzefiCalc log kaydı oluştur
    async createLog(userid, userHexData, options = {}) {
        try {
            const logData = {
                userid: userid,
                user_hex_data: userHexData,
                status: this.statusTypes.PENDING,
                server_ip: options.serverIp || null,
                user_agent: options.userAgent || null,
                session_id: options.sessionId || null,
                request_timestamp: new Date()
            };

            const result = await this.insertData(this.tableName, logData);
            
            return {
                success: true,
                logId: result.insertId,
                message: 'ZenzefiCalc log kaydı oluşturuldu',
                data: logData
            };
        } catch (error) {
            throw new Error(`ZenzefiCalc log oluşturulamadı: ${error.message}`);
        }
    }

    // Log durumunu güncelle
    async updateLogStatus(logId, status, options = {}) {
        try {
            if (!Object.values(this.statusTypes).includes(status)) {
                throw new Error(`Geçersiz status: ${status}`);
            }

            const updateData = {
                status: status,
                updated_at: new Date()
            };

            // Status'a göre ek alanları güncelle
            if (status === this.statusTypes.PROCESSING) {
                updateData.request_timestamp = new Date();
            } else if (status === this.statusTypes.COMPLETED) {
                updateData.response_timestamp = new Date();
                if (options.zenzefiHexResult) {
                    updateData.zenzefi_hex_result = options.zenzefiHexResult;
                }
                if (options.processingTimeMs) {
                    updateData.processing_time_ms = options.processingTimeMs;
                }
            } else if (status === this.statusTypes.FAILED || status === this.statusTypes.TIMEOUT) {
                updateData.response_timestamp = new Date();
                if (options.errorMessage) {
                    updateData.error_message = options.errorMessage;
                }
                if (options.processingTimeMs) {
                    updateData.processing_time_ms = options.processingTimeMs;
                }
            }

            const result = await this.updateData(this.tableName, updateData, { id: logId });
            
            return {
                success: true,
                affectedRows: result.affectedRows,
                message: `Log durumu ${status} olarak güncellendi`
            };
        } catch (error) {
            throw new Error(`Log durumu güncellenemedi: ${error.message}`);
        }
    }

    // ZenzefiServer sonucunu kaydet
    async completeLog(logId, zenzefiHexResult, processingTimeMs = null) {
        try {
            return await this.updateLogStatus(logId, this.statusTypes.COMPLETED, {
                zenzefiHexResult: zenzefiHexResult,
                processingTimeMs: processingTimeMs
            });
        } catch (error) {
            throw new Error(`Log tamamlanamadı: ${error.message}`);
        }
    }

    // Log'u başarısız olarak işaretle
    async failLog(logId, errorMessage, processingTimeMs = null) {
        try {
            return await this.updateLogStatus(logId, this.statusTypes.FAILED, {
                errorMessage: errorMessage,
                processingTimeMs: processingTimeMs
            });
        } catch (error) {
            throw new Error(`Log başarısız olarak işaretlenemedi: ${error.message}`);
        }
    }

    // Log'u timeout olarak işaretle
    async timeoutLog(logId, processingTimeMs = null) {
        try {
            return await this.updateLogStatus(logId, this.statusTypes.TIMEOUT, {
                errorMessage: 'ZenzefiServer yanıt vermedi (timeout)',
                processingTimeMs: processingTimeMs
            });
        } catch (error) {
            throw new Error(`Log timeout olarak işaretlenemedi: ${error.message}`);
        }
    }

    // Kullanıcının log geçmişini getir
    async getUserLogs(userid, options = {}) {
        try {
            const queryOptions = {
                where: { userid: userid },
                orderBy: options.orderBy || 'created_at DESC',
                limit: options.limit || 50
            };

            if (options.status) {
                queryOptions.where.status = options.status;
            }

            if (options.dateFrom) {
                // Date range için custom query kullanmak gerekebilir
            }

            return await this.selectData(this.tableName, queryOptions);
        } catch (error) {
            throw new Error(`Kullanıcı logları alınamadı: ${error.message}`);
        }
    }

    // Log detayını getir
    async getLogById(logId) {
        try {
            const result = await this.selectData(this.tableName, {
                where: { id: logId },
                limit: 1
            });

            return result.length > 0 ? result[0] : null;
        } catch (error) {
            throw new Error(`Log detayı alınamadı: ${error.message}`);
        }
    }

    // Bekleyen logları getir
    async getPendingLogs(limit = 10) {
        try {
            return await this.selectData(this.tableName, {
                where: { status: this.statusTypes.PENDING },
                orderBy: 'created_at ASC',
                limit: limit
            });
        } catch (error) {
            throw new Error(`Bekleyen loglar alınamadı: ${error.message}`);
        }
    }

    // İşleniyor durumundaki logları getir
    async getProcessingLogs() {
        try {
            return await this.selectData(this.tableName, {
                where: { status: this.statusTypes.PROCESSING },
                orderBy: 'request_timestamp ASC'
            });
        } catch (error) {
            throw new Error(`İşleniyor durumundaki loglar alınamadı: ${error.message}`);
        }
    }

    // Status istatistiklerini getir
    async getStatusStatistics() {
        try {
            return new Promise((resolve, reject) => {
                this.QueryExec({
                    query: "SELECT status, COUNT(*) as count FROM ZenzefiCalcLogs GROUP BY status ORDER BY count DESC",
                    params: []
                }, (result) => {
                    if (result) {
                        const stats = {};
                        result.forEach(row => {
                            stats[row.status] = row.count;
                        });
                        resolve(stats);
                    } else {
                        reject("Status istatistikleri alınamadı");
                    }
                });
            });
        } catch (error) {
            throw new Error(`Status istatistikleri alınamadı: ${error.message}`);
        }
    }

    // Günlük istatistikleri getir
    async getDailyStatistics(date = null) {
        try {
            const targetDate = date || new Date().toISOString().split('T')[0];
            
            return new Promise((resolve, reject) => {
                this.QueryExec({
                    query: `SELECT 
                        status, 
                        COUNT(*) as count,
                        AVG(processing_time_ms) as avg_processing_time
                    FROM ZenzefiCalcLogs 
                    WHERE DATE(created_at) = ? 
                    GROUP BY status`,
                    params: [targetDate]
                }, (result) => {
                    if (result) {
                        resolve(result);
                    } else {
                        reject("Günlük istatistikler alınamadı");
                    }
                });
            });
        } catch (error) {
            throw new Error(`Günlük istatistikler alınamadı: ${error.message}`);
        }
    }

    // Uzun süren işlemleri getir
    async getLongRunningLogs(thresholdMs = 30000) {
        try {
            return new Promise((resolve, reject) => {
                this.QueryExec({
                    query: `SELECT * FROM ZenzefiCalcLogs 
                    WHERE processing_time_ms > ? 
                    ORDER BY processing_time_ms DESC 
                    LIMIT 20`,
                    params: [thresholdMs]
                }, (result) => {
                    if (result) {
                        resolve(result);
                    } else {
                        reject("Uzun süren işlemler alınamadı");
                    }
                });
            });
        } catch (error) {
            throw new Error(`Uzun süren işlemler alınamadı: ${error.message}`);
        }
    }

    // Timeout olan logları temizle
    async cleanupTimeoutLogs(olderThanHours = 24) {
        try {
            return new Promise((resolve, reject) => {
                this.QueryExec({
                    query: `UPDATE ZenzefiCalcLogs 
                    SET status = 'timeout', 
                        error_message = 'Otomatik timeout - sistem temizliği',
                        response_timestamp = NOW()
                    WHERE status = 'processing' 
                    AND request_timestamp < DATE_SUB(NOW(), INTERVAL ? HOUR)`,
                    params: [olderThanHours]
                }, (result) => {
                    if (result) {
                        resolve({
                            success: true,
                            affectedRows: result.affectedRows,
                            message: `${result.affectedRows} timeout log temizlendi`
                        });
                    } else {
                        reject("Timeout loglar temizlenemedi");
                    }
                });
            });
        } catch (error) {
            throw new Error(`Timeout loglar temizlenemedi: ${error.message}`);
        }
    }

    // Eski logları sil
    async deleteOldLogs(olderThanDays = 30) {
        try {
            return new Promise((resolve, reject) => {
                this.QueryExec({
                    query: `DELETE FROM ZenzefiCalcLogs 
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)`,
                    params: [olderThanDays]
                }, (result) => {
                    if (result) {
                        resolve({
                            success: true,
                            affectedRows: result.affectedRows,
                            message: `${result.affectedRows} eski log silindi`
                        });
                    } else {
                        reject("Eski loglar silinemedi");
                    }
                });
            });
        } catch (error) {
            throw new Error(`Eski loglar silinemedi: ${error.message}`);
        }
    }

    // Kullanıcının son başarılı işlemini getir
    async getLastSuccessfulLog(userid) {
        try {
            const result = await this.selectData(this.tableName, {
                where: { userid: userid, status: this.statusTypes.COMPLETED },
                orderBy: 'response_timestamp DESC',
                limit: 1
            });

            return result.length > 0 ? result[0] : null;
        } catch (error) {
            throw new Error(`Son başarılı işlem alınamadı: ${error.message}`);
        }
    }

    // Sistem performans raporu
    async getPerformanceReport() {
        try {
            return new Promise((resolve, reject) => {
                this.QueryExec({
                    query: `SELECT 
                        COUNT(*) as total_logs,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_logs,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_logs,
                        COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_logs,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_logs,
                        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_logs,
                        AVG(processing_time_ms) as avg_processing_time,
                        MIN(processing_time_ms) as min_processing_time,
                        MAX(processing_time_ms) as max_processing_time
                    FROM ZenzefiCalcLogs 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`,
                    params: []
                }, (result) => {
                    if (result && result.length > 0) {
                        const report = result[0];
                        report.success_rate = report.total_logs > 0 ? 
                            ((report.completed_logs / report.total_logs) * 100).toFixed(2) : 0;
                        resolve(report);
                    } else {
                        reject("Performans raporu alınamadı");
                    }
                });
            });
        } catch (error) {
            throw new Error(`Performans raporu alınamadı: ${error.message}`);
        }
    }
}

export default ZenzefiLogsManager;
