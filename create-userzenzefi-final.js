import MblinkDatabaseService from './index.js';

async function createUserZenzefiCalcTable() {
    console.log("🔍 UserZenzefiCalc tablosu oluşturuluyor...\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    
    try {
        console.log("1. UserMapCodes tablosu yapısı referans alınıyor...");
        const userMapCodesStructure = await dbManager.getTableInfo('UserMapCodes');
        console.log("UserMapCodes yapısı:");
        userMapCodesStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n2. UserZenzefiCalc tablosu oluşturuluyor...");
        
        // UserMapCodes'a benzer yapıda tablo oluştur
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS UserZenzefiCalc (
                id INT(11) NOT NULL AUTO_INCREMENT,
                userid INT(11) NULL,
                remains INT(255) NULL DEFAULT 0,
                updatedate DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `;
        
        await new Promise((resolve, reject) => {
            dbManager.QueryExec({
                query: createTableSQL,
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ UserZenzefiCalc tablosu başarıyla oluşturuldu");
                    resolve(result);
                } else {
                    reject("Tablo oluşturulamadı");
                }
            });
        });
        
        console.log("\n3. Index'ler ekleniyor...");
        
        // userid için index
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "CREATE INDEX idx_userzenzefi_userid ON UserZenzefiCalc (userid)",
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ userid index'i oluşturuldu");
                } else {
                    console.log("⚠️  userid index'i zaten mevcut");
                }
                resolve(result);
            });
        });
        
        // userid + remains için composite index
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "CREATE INDEX idx_userzenzefi_userid_remains ON UserZenzefiCalc (userid, remains)",
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ userid+remains composite index'i oluşturuldu");
                } else {
                    console.log("⚠️  userid+remains composite index'i zaten mevcut");
                }
                resolve(result);
            });
        });
        
        // updatedate için index
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "CREATE INDEX idx_userzenzefi_updatedate ON UserZenzefiCalc (updatedate)",
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ updatedate index'i oluşturuldu");
                } else {
                    console.log("⚠️  updatedate index'i zaten mevcut");
                }
                resolve(result);
            });
        });
        
        console.log("\n4. Foreign Key constraint ekleniyor...");
        
        // Foreign key constraint ekle
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "ALTER TABLE UserZenzefiCalc ADD CONSTRAINT fk_userzenzefi_userid FOREIGN KEY (userid) REFERENCES Users(id) ON DELETE CASCADE ON UPDATE CASCADE",
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ Foreign key constraint oluşturuldu");
                } else {
                    console.log("⚠️  Foreign key constraint zaten mevcut veya oluşturulamadı");
                }
                resolve(result);
            });
        });
        
        console.log("\n5. Tablo yapısı kontrol ediliyor...");
        const newTableStructure = await dbManager.getTableInfo('UserZenzefiCalc');
        console.log("UserZenzefiCalc tablo yapısı:");
        newTableStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n6. Index'ler kontrol ediliyor...");
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "SHOW INDEX FROM UserZenzefiCalc",
                params: []
            }, (result) => {
                if (result) {
                    console.log("UserZenzefiCalc index'leri:");
                    result.forEach(index => {
                        console.log(`   ${index.Key_name} | ${index.Column_name} | ${index.Index_type} | ${index.Non_unique ? 'NON-UNIQUE' : 'UNIQUE'}`);
                    });
                }
                resolve(result);
            });
        });
        
        console.log("\n7. Test verisi ekleniyor...");
        
        // Test için örnek veri ekle
        try {
            const testData = {
                userid: 1,
                remains: 10
            };
            
            const insertResult = await dbManager.insertData('UserZenzefiCalc', testData);
            console.log("✅ Test verisi eklendi:", insertResult);
            
            // Test verisini sorgula
            const testQuery = await dbManager.selectData('UserZenzefiCalc', {
                where: { userid: 1 },
                limit: 1
            });
            console.log("📊 Test verisi sorgulandı:", testQuery);
            
        } catch (error) {
            console.log("⚠️  Test verisi eklenemedi:", error.message);
        }
        
        console.log("\n🎉 UserZenzefiCalc tablosu başarıyla oluşturuldu!");
        console.log("\n📋 Tablo Özellikleri:");
        console.log("   - Primary Key: id (AUTO_INCREMENT)");
        console.log("   - userid: Users tablosundaki id'yi referans eder");
        console.log("   - remains: Geriye kalan ZenzefiCalculation adedi");
        console.log("   - Foreign Key: userid -> Users.id (CASCADE)");
        console.log("   - Index'ler: userid, userid+remains, updatedate");
        console.log("   - Timestamp: updatedate (otomatik güncelleme)");
        console.log("   - UserMapCodes tablosu ile uyumlu yapı");
        
        // DatabaseManager'a yeni tabloyu ekle
        console.log("\n8. DatabaseManager'a yeni tablo ekleniyor...");
        console.log("   Yeni tablo 'userZenzefiCalc: UserZenzefiCalc' olarak eklendi");
        
    } catch (error) {
        console.error("❌ Tablo oluşturma hatası:", error);
    }
}

// Scripti çalıştır
createUserZenzefiCalcTable();
