import DBConnector from './src/dbconnector.js';

async function testConnection() {
    console.log("🔍 Veritabanı bağlantısı test ediliyor...");
    
    const db = new DBConnector();
    
    try {
        // Basit bir sorgu
        await new Promise((resolve, reject) => {
            db.QueryExec({
                query: "SELECT 1 as test",
                params: []
            }, (result) => {
                if (result) {
                    console.log("✅ Bağlantı başarılı:", result);
                    resolve(result);
                } else {
                    console.log("❌ Bağlantı başarısız");
                    reject("Bağlantı hatası");
                }
            });
        });
        
        // Tabloları listele
        await new Promise((resolve, reject) => {
            db.QueryExec({
                query: "SHOW TABLES",
                params: []
            }, (result) => {
                if (result) {
                    console.log(`✅ ${result.length} tablo bulundu`);
                    console.log("Son 5 tablo:");
                    result.slice(-5).forEach((table, index) => {
                        const tableName = Object.values(table)[0];
                        console.log(`   ${index + 1}. ${tableName}`);
                    });
                    resolve(result);
                } else {
                    reject("Tablolar listelenemedi");
                }
            });
        });
        
    } catch (error) {
        console.error("❌ Test hatası:", error);
    }
}

testConnection();
