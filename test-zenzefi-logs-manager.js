import MblinkDatabaseService from './index.js';

async function testZenzefiLogsManager() {
    console.log("🧪 ZenzefiLogsManager Test Başlatılıyor...\n");
    
    const service = new MblinkDatabaseService();
    const logsManager = service.getLogsManager();
    
    try {
        console.log("=".repeat(60));
        console.log("🔍 ZENZEFI LOGS MANAGER TEST SÜİTİ");
        console.log("=".repeat(60));

        // Test kullanıcısı ve HEX verisi
        const testUserId = 1;
        const testHexData = '48656C6C6F20576F726C6421'; // "Hello World!" in HEX
        const zenzefiResult = '21646C726F57206F6C6C6548'; // "!dlroW olleH" in HEX (reversed)
        
        console.log("\n1. 📝 Yeni log kaydı oluşturma:");
        const createResult = await logsManager.createLog(testUserId, testHexData, {
            serverIp: '*************',
            userAgent: 'MblinkClient/2.0',
            sessionId: 'test_session_' + Date.now()
        });
        console.log("   ✅ Log oluşturuldu:", createResult);
        const logId = createResult.logId;

        console.log("\n2. 🔄 Log durumunu 'processing' olarak güncelleme:");
        const processingResult = await logsManager.updateLogStatus(logId, 'processing');
        console.log("   ✅ Status güncellendi:", processingResult);

        console.log("\n3. ✅ Log'u başarılı olarak tamamlama:");
        const completeResult = await logsManager.completeLog(logId, zenzefiResult, 2500);
        console.log("   ✅ Log tamamlandı:", completeResult);

        console.log("\n4. 📊 Log detayını görüntüleme:");
        const logDetail = await logsManager.getLogById(logId);
        console.log("   📊 Log detayı:");
        console.log(`      ID: ${logDetail.id}`);
        console.log(`      UserID: ${logDetail.userid}`);
        console.log(`      User HEX: ${logDetail.user_hex_data}`);
        console.log(`      ZenzefiServer HEX: ${logDetail.zenzefi_hex_result}`);
        console.log(`      Status: ${logDetail.status}`);
        console.log(`      Processing Time: ${logDetail.processing_time_ms}ms`);
        console.log(`      Created: ${logDetail.created_at}`);
        console.log(`      Updated: ${logDetail.updated_at}`);

        console.log("\n5. ❌ Başarısız log testi:");
        const failLogResult = await logsManager.createLog(testUserId, 'INVALID_HEX_DATA', {
            serverIp: '*************',
            sessionId: 'fail_test_' + Date.now()
        });
        const failLogId = failLogResult.logId;
        
        await logsManager.updateLogStatus(failLogId, 'processing');
        const failResult = await logsManager.failLog(failLogId, 'Geçersiz HEX verisi', 500);
        console.log("   ❌ Başarısız log oluşturuldu:", failResult);

        console.log("\n6. ⏰ Timeout log testi:");
        const timeoutLogResult = await logsManager.createLog(testUserId, 'TIMEOUT_TEST_HEX', {
            serverIp: '*************',
            sessionId: 'timeout_test_' + Date.now()
        });
        const timeoutLogId = timeoutLogResult.logId;
        
        await logsManager.updateLogStatus(timeoutLogId, 'processing');
        const timeoutResult = await logsManager.timeoutLog(timeoutLogId, 30000);
        console.log("   ⏰ Timeout log oluşturuldu:", timeoutResult);

        console.log("\n7. 📋 Kullanıcı loglarını listeleme:");
        const userLogs = await logsManager.getUserLogs(testUserId, { limit: 5 });
        console.log("   📋 Kullanıcı logları (son 5):");
        userLogs.forEach((log, index) => {
            console.log(`      ${index + 1}. ID: ${log.id}, Status: ${log.status}, Created: ${log.created_at}`);
        });

        console.log("\n8. ⏳ Bekleyen logları getirme:");
        const pendingLogs = await logsManager.getPendingLogs(3);
        console.log(`   ⏳ ${pendingLogs.length} bekleyen log bulundu`);

        console.log("\n9. 🔄 İşleniyor durumundaki logları getirme:");
        const processingLogs = await logsManager.getProcessingLogs();
        console.log(`   🔄 ${processingLogs.length} işleniyor durumunda log bulundu`);

        console.log("\n10. 📊 Status istatistikleri:");
        const statusStats = await logsManager.getStatusStatistics();
        console.log("   📊 Status dağılımı:");
        Object.entries(statusStats).forEach(([status, count]) => {
            console.log(`      ${status}: ${count} kayıt`);
        });

        console.log("\n11. 📅 Günlük istatistikler:");
        const dailyStats = await logsManager.getDailyStatistics();
        console.log("   📅 Bugünkü istatistikler:");
        dailyStats.forEach(stat => {
            console.log(`      ${stat.status}: ${stat.count} kayıt, Ort. süre: ${stat.avg_processing_time || 'N/A'}ms`);
        });

        console.log("\n12. 🐌 Uzun süren işlemler:");
        const longRunningLogs = await logsManager.getLongRunningLogs(1000);
        console.log(`   🐌 ${longRunningLogs.length} uzun süren işlem bulundu`);
        if (longRunningLogs.length > 0) {
            longRunningLogs.slice(0, 3).forEach((log, index) => {
                console.log(`      ${index + 1}. ID: ${log.id}, Süre: ${log.processing_time_ms}ms`);
            });
        }

        console.log("\n13. 🏆 Son başarılı işlem:");
        const lastSuccess = await logsManager.getLastSuccessfulLog(testUserId);
        if (lastSuccess) {
            console.log(`   🏆 Son başarılı işlem: ID ${lastSuccess.id}, ${lastSuccess.response_timestamp}`);
        } else {
            console.log("   ⚠️  Başarılı işlem bulunamadı");
        }

        console.log("\n14. 📈 Performans raporu:");
        const performanceReport = await logsManager.getPerformanceReport();
        console.log("   📈 Son 24 saat performans raporu:");
        console.log(`      Toplam log: ${performanceReport.total_logs}`);
        console.log(`      Başarılı: ${performanceReport.completed_logs}`);
        console.log(`      Başarısız: ${performanceReport.failed_logs}`);
        console.log(`      Timeout: ${performanceReport.timeout_logs}`);
        console.log(`      Bekleyen: ${performanceReport.pending_logs}`);
        console.log(`      İşleniyor: ${performanceReport.processing_logs}`);
        console.log(`      Başarı oranı: %${performanceReport.success_rate}`);
        console.log(`      Ort. işlem süresi: ${performanceReport.avg_processing_time || 'N/A'}ms`);

        console.log("\n15. 🧹 Sistem temizliği testi:");
        try {
            const cleanupResult = await logsManager.cleanupTimeoutLogs(0.01); // 0.01 saat = 36 saniye
            console.log("   🧹 Timeout temizliği:", cleanupResult);
        } catch (error) {
            console.log("   ⚠️  Timeout temizliği:", error.message);
        }

        console.log("\n" + "=".repeat(60));
        console.log("🎉 TÜM TESTLER BAŞARIYLA TAMAMLANDI!");
        console.log("=".repeat(60));
        
        console.log("\n📋 Test Özeti:");
        console.log("   ✅ Log oluşturma işlemleri");
        console.log("   ✅ Status güncelleme işlemleri");
        console.log("   ✅ Başarılı/Başarısız/Timeout log yönetimi");
        console.log("   ✅ Kullanıcı log geçmişi");
        console.log("   ✅ Status ve performans istatistikleri");
        console.log("   ✅ Sistem temizlik işlemleri");
        console.log("   ✅ Backend server entegrasyonu hazır");
        
        console.log("\n🚀 ZenzefiLogsManager hazır ve çalışıyor!");
        console.log("📝 Backend server'dan gelen HEX verileri güvenli şekilde saklanabilir!");

    } catch (error) {
        console.error("❌ Test hatası:", error);
    }
}

// Test'i çalıştır
testZenzefiLogsManager();
