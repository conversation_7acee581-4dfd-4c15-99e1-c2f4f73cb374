import MblinkDatabaseService from './index.js';

async function createZenzefiCalcLogsTable() {
    console.log("🔍 ZenzefiCalcLogs tablosu oluşturuluyor...\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    
    try {
        console.log("1. ZenzefiCalcLogs tablosu oluşturuluyor...");
        
        // Basit SQL ile tablo oluştur
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS ZenzefiCalcLogs (
                id INT(11) NOT NULL AUTO_INCREMENT,
                userid INT(11) NOT NULL,
                user_hex_data TEXT NULL,
                zenzefi_hex_result TEXT NULL,
                status ENUM('pending', 'processing', 'completed', 'failed', 'timeout') NOT NULL DEFAULT 'pending',
                error_message TEXT NULL,
                request_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                response_timestamp DATETIME NULL,
                processing_time_ms INT(11) NULL,
                server_ip VARCHAR(45) NULL,
                user_agent TEXT NULL,
                session_id VARCHAR(255) NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `;
        
        await new Promise((resolve, reject) => {
            dbManager.QueryExec({
                query: createTableSQL,
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ ZenzefiCalcLogs tablosu başarıyla oluşturuldu");
                    resolve(result);
                } else {
                    reject("Tablo oluşturulamadı");
                }
            });
        });
        
        console.log("\n2. Index'ler ekleniyor...");
        
        // Index'leri ayrı ayrı ekle
        const indexes = [
            "CREATE INDEX idx_zenzefi_logs_userid ON ZenzefiCalcLogs (userid)",
            "CREATE INDEX idx_zenzefi_logs_status ON ZenzefiCalcLogs (status)",
            "CREATE INDEX idx_zenzefi_logs_created_at ON ZenzefiCalcLogs (created_at)",
            "CREATE INDEX idx_zenzefi_logs_userid_status ON ZenzefiCalcLogs (userid, status)",
            "CREATE INDEX idx_zenzefi_logs_status_created ON ZenzefiCalcLogs (status, created_at)"
        ];
        
        for (const indexSQL of indexes) {
            await new Promise((resolve) => {
                dbManager.QueryExec({
                    query: indexSQL,
                    params: []
                }, (result) => {
                    if (result !== null) {
                        console.log(`✅ Index oluşturuldu: ${indexSQL.split(' ')[2]}`);
                    } else {
                        console.log(`⚠️  Index zaten mevcut: ${indexSQL.split(' ')[2]}`);
                    }
                    resolve(result);
                });
            });
        }
        
        console.log("\n3. Foreign Key constraint ekleniyor...");
        
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "ALTER TABLE ZenzefiCalcLogs ADD CONSTRAINT fk_zenzefi_logs_userid FOREIGN KEY (userid) REFERENCES Users(id) ON DELETE CASCADE ON UPDATE CASCADE",
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ Foreign key constraint oluşturuldu");
                } else {
                    console.log("⚠️  Foreign key constraint zaten mevcut");
                }
                resolve(result);
            });
        });
        
        console.log("\n4. Tablo yapısı kontrol ediliyor...");
        const tableStructure = await dbManager.getTableInfo('ZenzefiCalcLogs');
        console.log("ZenzefiCalcLogs tablo yapısı:");
        tableStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key}`);
        });
        
        console.log("\n5. Test verisi ekleniyor...");
        
        try {
            const testData = {
                userid: 1,
                user_hex_data: '48656C6C6F20576F726C64', // "Hello World" in HEX
                status: 'pending',
                server_ip: '127.0.0.1',
                user_agent: 'MblinkTestClient/1.0',
                session_id: 'test_session_' + Date.now()
            };
            
            const insertResult = await dbManager.insertData('ZenzefiCalcLogs', testData);
            console.log("✅ Test verisi eklendi:", insertResult);
            
            // Status güncelleme
            const updateResult = await dbManager.updateData('ZenzefiCalcLogs', 
                { 
                    status: 'completed',
                    zenzefi_hex_result: '576F726C6420486F6C6C65',
                    response_timestamp: new Date(),
                    processing_time_ms: 1250
                },
                { id: insertResult.insertId }
            );
            console.log("✅ Test verisi güncellendi:", updateResult);
            
            // Test verisini sorgula
            const testQuery = await dbManager.selectData('ZenzefiCalcLogs', {
                where: { id: insertResult.insertId },
                limit: 1
            });
            
            if (testQuery.length > 0) {
                const record = testQuery[0];
                console.log("📊 Test verisi:");
                console.log(`   ID: ${record.id}`);
                console.log(`   UserID: ${record.userid}`);
                console.log(`   User HEX: ${record.user_hex_data}`);
                console.log(`   ZenzefiServer HEX: ${record.zenzefi_hex_result}`);
                console.log(`   Status: ${record.status}`);
                console.log(`   Created: ${record.created_at}`);
                console.log(`   Updated: ${record.updated_at}`);
                console.log(`   Processing Time: ${record.processing_time_ms}ms`);
            }
            
        } catch (error) {
            console.log("⚠️  Test verisi hatası:", error.message);
        }
        
        console.log("\n🎉 ZenzefiCalcLogs tablosu başarıyla oluşturuldu!");
        console.log("\n📋 Tablo Özellikleri:");
        console.log("   ✅ userid: Users tablosuna referans");
        console.log("   ✅ user_hex_data: Kullanıcı HEX verileri");
        console.log("   ✅ zenzefi_hex_result: ZenzefiServer HEX sonuçları");
        console.log("   ✅ status: İşlem durumu (pending/processing/completed/failed/timeout)");
        console.log("   ✅ created_at: Kayıt oluşturulma zamanı");
        console.log("   ✅ updated_at: Son güncelleme zamanı");
        console.log("   ✅ Tracking: server_ip, user_agent, session_id");
        console.log("   ✅ Performance: processing_time_ms");
        console.log("   ✅ Error handling: error_message");
        
    } catch (error) {
        console.error("❌ Tablo oluşturma hatası:", error);
    }
}

// Scripti çalıştır
createZenzefiCalcLogsTable();
