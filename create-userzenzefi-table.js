import MblinkDatabaseService from './index.js';

async function createUserZenzefiCalcTable() {
    console.log("🔍 UserZenzefiCalc tablosu oluşturuluyor...\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    
    try {
        // Önce UserMapCodes tablosunun yapısını inceleyelim
        console.log("1. UserMapCodes tablosu yapısı inceleniyor...");
        const userMapCodesStructure = await dbManager.getTableInfo('UserMapCodes');
        console.log("UserMapCodes yapısı:");
        userMapCodesStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n2. Users tablosu yapısı kontrol ediliyor...");
        const usersStructure = await dbManager.getTableInfo('Users');
        console.log("Users tablosu id kolonu:");
        const userIdField = usersStructure.find(field => field.Field === 'id');
        console.log(`   ${userIdField.Field} | ${userIdField.Type} | ${userIdField.Null} | ${userIdField.Key} | ${userIdField.Default} | ${userIdField.Extra}`);
        
        console.log("\n3. UserZenzefiCalc tablosu oluşturuluyor...");
        
        // UserZenzefiCalc tablosu için kolon tanımları
        const columns = [
            {
                name: 'id',
                type: 'INT(11)',
                notNull: true,
                primaryKey: true,
                autoIncrement: true
            },
            {
                name: 'userid',
                type: 'INT(11)',
                notNull: true
            },
            {
                name: 'remains',
                type: 'INT(11)',
                notNull: true,
                default: 0
            },
            {
                name: 'created_at',
                type: 'DATETIME',
                notNull: true,
                default: 'CURRENT_TIMESTAMP'
            },
            {
                name: 'updated_at',
                type: 'DATETIME',
                notNull: true,
                default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            }
        ];
        
        // Tabloyu oluştur
        const createResult = await dbManager.createTable('UserZenzefiCalc', columns);
        console.log("✅", createResult);
        
        console.log("\n4. Index'ler oluşturuluyor...");
        
        // userid için index oluştur
        await dbManager.QueryExec({
            query: "CREATE INDEX idx_userzenzefi_userid ON UserZenzefiCalc (userid)",
            params: []
        }, (result) => {
            if (result !== null) {
                console.log("✅ userid index'i oluşturuldu");
            } else {
                console.log("⚠️  userid index'i zaten mevcut veya oluşturulamadı");
            }
        });
        
        // userid + remains için composite index
        await dbManager.QueryExec({
            query: "CREATE INDEX idx_userzenzefi_userid_remains ON UserZenzefiCalc (userid, remains)",
            params: []
        }, (result) => {
            if (result !== null) {
                console.log("✅ userid+remains composite index'i oluşturuldu");
            } else {
                console.log("⚠️  userid+remains composite index'i zaten mevcut veya oluşturulamadı");
            }
        });
        
        // created_at için index
        await dbManager.QueryExec({
            query: "CREATE INDEX idx_userzenzefi_created_at ON UserZenzefiCalc (created_at)",
            params: []
        }, (result) => {
            if (result !== null) {
                console.log("✅ created_at index'i oluşturuldu");
            } else {
                console.log("⚠️  created_at index'i zaten mevcut veya oluşturulamadı");
            }
        });
        
        console.log("\n5. Foreign Key constraint oluşturuluyor...");
        
        // Foreign key constraint ekle
        await dbManager.QueryExec({
            query: "ALTER TABLE UserZenzefiCalc ADD CONSTRAINT fk_userzenzefi_userid FOREIGN KEY (userid) REFERENCES Users(id) ON DELETE CASCADE ON UPDATE CASCADE",
            params: []
        }, (result) => {
            if (result !== null) {
                console.log("✅ Foreign key constraint oluşturuldu");
            } else {
                console.log("⚠️  Foreign key constraint zaten mevcut veya oluşturulamadı");
            }
        });
        
        console.log("\n6. Tablo yapısı kontrol ediliyor...");
        const newTableStructure = await dbManager.getTableInfo('UserZenzefiCalc');
        console.log("UserZenzefiCalc tablo yapısı:");
        newTableStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n7. Index'ler kontrol ediliyor...");
        await dbManager.QueryExec({
            query: "SHOW INDEX FROM UserZenzefiCalc",
            params: []
        }, (result) => {
            if (result) {
                console.log("UserZenzefiCalc index'leri:");
                result.forEach(index => {
                    console.log(`   ${index.Key_name} | ${index.Column_name} | ${index.Index_type} | ${index.Non_unique ? 'NON-UNIQUE' : 'UNIQUE'}`);
                });
            }
        });
        
        console.log("\n8. Test verisi ekleniyor...");
        
        // Test için örnek veri ekle (userid=1 varsa)
        try {
            const testData = {
                userid: 1,
                remains: 10
            };
            
            const insertResult = await dbManager.insertData('UserZenzefiCalc', testData);
            console.log("✅ Test verisi eklendi:", insertResult);
            
            // Test verisini sorgula
            const testQuery = await dbManager.selectData('UserZenzefiCalc', {
                where: { userid: 1 },
                limit: 1
            });
            console.log("📊 Test verisi sorgulandı:", testQuery);
            
        } catch (error) {
            console.log("⚠️  Test verisi eklenemedi (userid=1 mevcut değil olabilir):", error.message);
        }
        
        console.log("\n🎉 UserZenzefiCalc tablosu başarıyla oluşturuldu!");
        console.log("\n📋 Tablo Özellikleri:");
        console.log("   - Primary Key: id (AUTO_INCREMENT)");
        console.log("   - Foreign Key: userid -> Users.id (CASCADE)");
        console.log("   - Index'ler: userid, userid+remains, created_at");
        console.log("   - Timestamp'ler: created_at, updated_at (otomatik)");
        
    } catch (error) {
        console.error("❌ Tablo oluşturma hatası:", error);
    }
}

// Scripti çalıştır
createUserZenzefiCalcTable();
