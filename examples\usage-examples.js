import MblinkDatabaseService from '../index.js';

async function demonstrateUsage() {
    console.log("📚 Mblink Database Service - Kullanım Örnekleri\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    const procManager = service.getProcManager();

    try {
        // 1. Database Manager Örnekleri
        console.log("=".repeat(50));
        console.log("🗃️  DATABASE MANAGER ÖRNEKLERİ");
        console.log("=".repeat(50));

        // Tablo bilgisi alma
        console.log("\n1. Tablo bilgisi alma:");
        const userTableInfo = await dbManager.getTableInfo('Users');
        console.log("Users tablosu yapısı:", userTableInfo.slice(0, 3)); // İlk 3 kolon

        // Veri sorgulama (güvenli)
        console.log("\n2. Veri sorgulama (TokenTypes tablosu):");
        const tokenTypes = await dbManager.selectData('TokenTypes', { 
            limit: 5,
            orderBy: 'id'
        });
        console.log("Token türleri:", tokenTypes);

        // 2. Procedure Manager Örnekleri
        console.log("\n" + "=".repeat(50));
        console.log("⚙️  PROCEDURE MANAGER ÖRNEKLERİ");
        console.log("=".repeat(50));

        // Mevcut procedure çağırma
        console.log("\n1. Mevcut procedure çağırma (tokenTypeList):");
        const tokenTypeList = await procManager.callProcedure('tokenTypeList');
        console.log("Token türü listesi:", tokenTypeList);

        // Procedure detayı alma
        console.log("\n2. Procedure detayı alma:");
        try {
            const procDetail = await procManager.getProcedureDetail('tokenTypeList');
            console.log("Procedure detayı alındı:", procDetail['Create Procedure'] ? "✅" : "❌");
        } catch (error) {
            console.log("Procedure detayı alınamadı:", error.message);
        }

        // 3. Yeni Procedure Oluşturma Örneği
        console.log("\n" + "=".repeat(50));
        console.log("🆕 YENİ PROCEDURE OLUŞTURMA");
        console.log("=".repeat(50));

        console.log("\n1. Test procedure oluşturuluyor...");
        const testProcName = 'mblink_test_getSystemInfo';
        const testProcParams = [];
        const testProcBody = `
            DECLARE tableCount INT DEFAULT 0;
            DECLARE procCount INT DEFAULT 0;
            
            SELECT COUNT(*) INTO tableCount 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE();
            
            SELECT COUNT(*) INTO procCount 
            FROM information_schema.routines 
            WHERE routine_schema = DATABASE() AND routine_type = 'PROCEDURE';
            
            SELECT 
                DATABASE() as database_name,
                tableCount as total_tables,
                procCount as total_procedures,
                NOW() as check_time;
        `;
        const testProcOptions = {
            comment: 'Test procedure - sistem bilgilerini döndürür',
            readSqlData: true
        };

        try {
            const createResult = await procManager.safeCreateProcedure(
                testProcName, 
                testProcParams, 
                testProcBody, 
                testProcOptions
            );
            console.log("✅", createResult);

            // Oluşturulan procedure'ü test et
            console.log("\n2. Oluşturulan procedure test ediliyor...");
            const testResult = await procManager.callProcedure(testProcName);
            console.log("Test sonucu:", testResult);

            // Procedure'ü temizle
            console.log("\n3. Test procedure temizleniyor...");
            const dropResult = await procManager.dropProcedure(testProcName);
            console.log("✅", dropResult);

        } catch (error) {
            console.log("❌ Test procedure işlemi hatası:", error.message);
        }

        // 4. Function Oluşturma Örneği
        console.log("\n" + "=".repeat(50));
        console.log("🔧 YENİ FUNCTION OLUŞTURMA");
        console.log("=".repeat(50));

        console.log("\n1. Test function oluşturuluyor...");
        const testFuncName = 'mblink_test_calculateDays';
        const testFuncParams = [
            { name: 'startDate', type: 'DATE' },
            { name: 'endDate', type: 'DATE' }
        ];
        const testFuncReturnType = 'INT';
        const testFuncBody = `
            RETURN DATEDIFF(endDate, startDate);
        `;
        const testFuncOptions = {
            comment: 'Test function - iki tarih arasındaki gün sayısını hesaplar',
            deterministic: true
        };

        try {
            const createFuncResult = await procManager.createFunction(
                testFuncName,
                testFuncParams,
                testFuncReturnType,
                testFuncBody,
                testFuncOptions
            );
            console.log("✅", createFuncResult);

            // Function'ı temizle
            console.log("\n2. Test function temizleniyor...");
            const dropFuncResult = await procManager.dropFunction(testFuncName);
            console.log("✅", dropFuncResult);

        } catch (error) {
            console.log("❌ Test function işlemi hatası:", error.message);
        }

        // 5. Güvenlik Örnekleri
        console.log("\n" + "=".repeat(50));
        console.log("🔒 GÜVENLİK ÖRNEKLERİ");
        console.log("=".repeat(50));

        console.log("\n1. Mevcut sistem procedure'ünü silmeye çalışma:");
        try {
            await procManager.dropProcedure('tokenCheck');
        } catch (error) {
            console.log("✅ Güvenlik koruması çalıştı:", error.message);
        }

        console.log("\n2. Mevcut sistem procedure'ünü değiştirmeye çalışma:");
        try {
            await procManager.createProcedure('loginUser', [], 'SELECT "test";');
        } catch (error) {
            console.log("✅ Güvenlik koruması çalıştı:", error.message);
        }

        console.log("\n🎉 Tüm örnekler başarıyla tamamlandı!");
        console.log("\n📝 Not: Bu örnekler canlı sistemde güvenli şekilde çalıştırılmıştır.");

    } catch (error) {
        console.error("❌ Örnek çalıştırma hatası:", error);
    }
}

// Örnekleri çalıştır
demonstrateUsage();
