"use strict"
/*************************
    Mblink Database Manager
    Coded by <PERSON><PERSON>
**************************/

import DBConnector from './dbconnector.js';

class DatabaseManager extends DBConnector {
    constructor() {
        super();
        this.tables = {
            // Kullanıcı Yönetimi
            users: 'Users',
            userLogins: 'UserLogins',
            userSubs: 'UserSubs',
            userTokens: 'UserTokens',
            userMapCodes: 'UserMapCodes',
            userZenzefiCalc: 'UserZenzefiCalc',
            zenzefiCalcLogs: 'ZenzefiCalcLogs',
            userOneTimeToken: 'UserOneTimeToken',
            
            // Token Sistemi
            tokenBuy: 'TokenBuy',
            tokenPrice: 'TokenPrice',
            tokenSpent: 'TokenSpent',
            tokenTypes: 'TokenTypes',
            
            // ECU Yönetimi
            ecuNames: 'ECU_Names',
            ecuErrors: 'ECU_Errors',
            ecuDiagCodes: 'ECU_DiagCodes',
            ecuVariantCodings: 'ECU_VariantCodings',
            ecuDaigVersions: 'EcuDaigVersions',
            ecuVariantBackups: 'EcuVariantBackups',
            ecuVariantIDs: 'EcuVariantIDs',
            
            // Map Kodları
            mapCodes: 'MapCodes',
            mapCodeTypes: 'MapCodeTypes',
            mapCodesExclude: 'MapCodesExclude',
            
            // Güvenlik
            antiTheftCodes: 'AntiTheftCodes',
            seedKeys: 'SeedKeys',
            vinSaCodes: 'VINSaCodes',
            
            // Flashware ve Katalog
            flashwares: 'Flashwares',
            catalogue: 'Catalogue',
            designation: 'Designation',
            
            // Diğer
            headUnitHU5S1: 'HeadUnit_HU5S1',
            keygenRequests: 'KeygenRequests',
            logsSeedKey: 'LogsSeedKey',
            skgtRequest: 'SkgtRequest',
            vgsScnTable: 'VgsScnTable'
        };
    }

    // Tablo bilgilerini getir
    async getTableInfo(tableName) {
        return new Promise((resolve, reject) => {
            const query = {
                query: "DESCRIBE ??",
                params: [tableName]
            };
            
            this.QueryExec(query, (result) => {
                if (result) {
                    resolve(result);
                } else {
                    reject(`${tableName} tablo bilgisi alınamadı`);
                }
            });
        });
    }

    // Tüm tabloları listele
    async getAllTables() {
        return new Promise((resolve, reject) => {
            const query = {
                query: "SHOW TABLES",
                params: []
            };
            
            this.QueryExec(query, (result) => {
                if (result) {
                    resolve(result.map(row => Object.values(row)[0]));
                } else {
                    reject("Tablolar listelenemedi");
                }
            });
        });
    }

    // Tablo oluştur
    async createTable(tableName, columns) {
        return new Promise((resolve, reject) => {
            const columnDefinitions = columns.map(col => {
                let definition = `${col.name} ${col.type}`;
                if (col.notNull) definition += ' NOT NULL';
                if (col.primaryKey) definition += ' PRIMARY KEY';
                if (col.autoIncrement) definition += ' AUTO_INCREMENT';
                if (col.unique) definition += ' UNIQUE';
                if (col.default !== undefined) definition += ` DEFAULT ${col.default}`;
                return definition;
            }).join(', ');

            const query = {
                query: `CREATE TABLE IF NOT EXISTS ?? (${columnDefinitions})`,
                params: [tableName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${tableName} tablosu başarıyla oluşturuldu`);
                } else {
                    reject(`${tableName} tablosu oluşturulamadı`);
                }
            });
        });
    }

    // Tablo sil
    async dropTable(tableName) {
        return new Promise((resolve, reject) => {
            const query = {
                query: "DROP TABLE IF EXISTS ??",
                params: [tableName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${tableName} tablosu başarıyla silindi`);
                } else {
                    reject(`${tableName} tablosu silinemedi`);
                }
            });
        });
    }

    // Tabloya kolon ekle
    async addColumn(tableName, columnName, columnType, options = {}) {
        return new Promise((resolve, reject) => {
            let definition = `${columnName} ${columnType}`;
            if (options.notNull) definition += ' NOT NULL';
            if (options.unique) definition += ' UNIQUE';
            if (options.default !== undefined) definition += ` DEFAULT ${options.default}`;
            if (options.after) definition += ` AFTER ${options.after}`;

            const query = {
                query: `ALTER TABLE ?? ADD COLUMN ${definition}`,
                params: [tableName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${tableName} tablosuna ${columnName} kolonu eklendi`);
                } else {
                    reject(`${tableName} tablosuna kolon eklenemedi`);
                }
            });
        });
    }

    // Kolondan kolon sil
    async dropColumn(tableName, columnName) {
        return new Promise((resolve, reject) => {
            const query = {
                query: "ALTER TABLE ?? DROP COLUMN ??",
                params: [tableName, columnName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${tableName} tablosundan ${columnName} kolonu silindi`);
                } else {
                    reject(`${tableName} tablosundan kolon silinemedi`);
                }
            });
        });
    }

    // Veri ekle
    async insertData(tableName, data) {
        return new Promise((resolve, reject) => {
            const columns = Object.keys(data);
            const values = Object.values(data);
            const placeholders = new Array(values.length).fill('?').join(', ');

            const query = {
                query: `INSERT INTO ?? (${columns.join(', ')}) VALUES (${placeholders})`,
                params: [tableName, ...values]
            };
            
            this.QueryExec(query, (result) => {
                if (result && result.affectedRows > 0) {
                    resolve({
                        success: true,
                        insertId: result.insertId,
                        affectedRows: result.affectedRows
                    });
                } else {
                    reject(`${tableName} tablosuna veri eklenemedi`);
                }
            });
        });
    }

    // Veri güncelle
    async updateData(tableName, data, whereCondition) {
        return new Promise((resolve, reject) => {
            const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
            const whereClause = Object.keys(whereCondition).map(key => `${key} = ?`).join(' AND ');
            
            const query = {
                query: `UPDATE ?? SET ${setClause} WHERE ${whereClause}`,
                params: [tableName, ...Object.values(data), ...Object.values(whereCondition)]
            };
            
            this.QueryExec(query, (result) => {
                if (result && result.affectedRows >= 0) {
                    resolve({
                        success: true,
                        affectedRows: result.affectedRows
                    });
                } else {
                    reject(`${tableName} tablosunda veri güncellenemedi`);
                }
            });
        });
    }

    // Veri sil
    async deleteData(tableName, whereCondition) {
        return new Promise((resolve, reject) => {
            const whereClause = Object.keys(whereCondition).map(key => `${key} = ?`).join(' AND ');
            
            const query = {
                query: `DELETE FROM ?? WHERE ${whereClause}`,
                params: [tableName, ...Object.values(whereCondition)]
            };
            
            this.QueryExec(query, (result) => {
                if (result && result.affectedRows >= 0) {
                    resolve({
                        success: true,
                        affectedRows: result.affectedRows
                    });
                } else {
                    reject(`${tableName} tablosundan veri silinemedi`);
                }
            });
        });
    }

    // Veri sorgula
    async selectData(tableName, options = {}) {
        return new Promise((resolve, reject) => {
            let query = `SELECT ${options.columns || '*'} FROM ??`;
            let params = [tableName];

            if (options.where) {
                const whereClause = Object.keys(options.where).map(key => `${key} = ?`).join(' AND ');
                query += ` WHERE ${whereClause}`;
                params.push(...Object.values(options.where));
            }

            if (options.orderBy) {
                query += ` ORDER BY ${options.orderBy}`;
            }

            if (options.limit) {
                query += ` LIMIT ${options.limit}`;
            }

            const queryObj = { query, params };
            
            this.QueryExec(queryObj, (result) => {
                if (result) {
                    resolve(result);
                } else {
                    reject(`${tableName} tablosundan veri sorgulanamadı`);
                }
            });
        });
    }

    // Tablo verilerini temizle
    async truncateTable(tableName) {
        return new Promise((resolve, reject) => {
            const query = {
                query: "TRUNCATE TABLE ??",
                params: [tableName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${tableName} tablosu başarıyla temizlendi`);
                } else {
                    reject(`${tableName} tablosu temizlenemedi`);
                }
            });
        });
    }
}

export default DatabaseManager;
