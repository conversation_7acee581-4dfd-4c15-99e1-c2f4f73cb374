import DBConnector from './src/dbconnector.js';

const db = new DBConnector();

// Tabloları listele
function getTables() {
    return new Promise((resolve, reject) => {
        const query = {
            query: "SHOW TABLES",
            params: []
        };
        
        db.QueryExec(query, (result) => {
            if (result) {
                resolve(result);
            } else {
                reject("Tablolar alınamadı");
            }
        });
    });
}

// Tablo yapısını göster
function getTableStructure(tableName) {
    return new Promise((resolve, reject) => {
        const query = {
            query: "DESCRIBE ??",
            params: [tableName]
        };
        
        db.QueryExec(query, (result) => {
            if (result) {
                resolve(result);
            } else {
                reject(`${tableName} tablo yapısı alınamadı`);
            }
        });
    });
}

// Stored Procedure'leri listele
function getProcedures() {
    return new Promise((resolve, reject) => {
        const query = {
            query: "SHOW PROCEDURE STATUS WHERE Db = ?",
            params: ['xentry']
        };
        
        db.QueryExec(query, (result) => {
            if (result) {
                resolve(result);
            } else {
                reject("Procedure'ler alınamadı");
            }
        });
    });
}

// Function'ları listele
function getFunctions() {
    return new Promise((resolve, reject) => {
        const query = {
            query: "SHOW FUNCTION STATUS WHERE Db = ?",
            params: ['xentry']
        };
        
        db.QueryExec(query, (result) => {
            if (result) {
                resolve(result);
            } else {
                reject("Function'lar alınamadı");
            }
        });
    });
}

// Procedure detayını göster
function getProcedureDetail(procedureName) {
    return new Promise((resolve, reject) => {
        const query = {
            query: "SHOW CREATE PROCEDURE ??",
            params: [procedureName]
        };
        
        db.QueryExec(query, (result) => {
            if (result) {
                resolve(result);
            } else {
                reject(`${procedureName} procedure detayı alınamadı`);
            }
        });
    });
}

// Ana inceleme fonksiyonu
async function inspectDatabase() {
    try {
        console.log("=== MBLINK DATABASE İNCELEMESİ ===\n");
        
        // Tabloları listele
        console.log("📋 TABLOLAR:");
        console.log("=".repeat(50));
        const tables = await getTables();
        tables.forEach((table, index) => {
            const tableName = Object.values(table)[0];
            console.log(`${index + 1}. ${tableName}`);
        });
        
        console.log("\n");
        
        // İlk birkaç tablonun yapısını göster
        console.log("🏗️  TABLO YAPILARI (İlk 3 tablo):");
        console.log("=".repeat(50));
        for (let i = 0; i < Math.min(3, tables.length); i++) {
            const tableName = Object.values(tables[i])[0];
            console.log(`\n--- ${tableName.toUpperCase()} ---`);
            try {
                const structure = await getTableStructure(tableName);
                structure.forEach(field => {
                    console.log(`  ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default}`);
                });
            } catch (error) {
                console.log(`  Hata: ${error}`);
            }
        }
        
        console.log("\n");
        
        // Stored Procedure'leri listele
        console.log("⚙️  STORED PROCEDURES:");
        console.log("=".repeat(50));
        try {
            const procedures = await getProcedures();
            if (procedures.length > 0) {
                procedures.forEach((proc, index) => {
                    console.log(`${index + 1}. ${proc.Name} (${proc.Type})`);
                });
            } else {
                console.log("Stored procedure bulunamadı.");
            }
        } catch (error) {
            console.log(`Hata: ${error}`);
        }
        
        console.log("\n");
        
        // Function'ları listele
        console.log("🔧 FUNCTIONS:");
        console.log("=".repeat(50));
        try {
            const functions = await getFunctions();
            if (functions.length > 0) {
                functions.forEach((func, index) => {
                    console.log(`${index + 1}. ${func.Name} (${func.Type})`);
                });
            } else {
                console.log("Function bulunamadı.");
            }
        } catch (error) {
            console.log(`Hata: ${error}`);
        }
        
        console.log("\n=== İNCELEME TAMAMLANDI ===");
        
    } catch (error) {
        console.error("Veritabanı incelemesi sırasında hata:", error);
    }
}

// Scripti çalıştır
inspectDatabase();
