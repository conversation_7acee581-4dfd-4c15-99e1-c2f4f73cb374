"use strict"
/*************************
    Mblink Procedure Manager
    Coded by <PERSON><PERSON>
**************************/

import DBConnector from './dbconnector.js';

class ProcedureManager extends DBConnector {
    constructor() {
        super();
        this.existingProcedures = [
            'antitheftcodeAdd', 'antitheftcodeGet', 'catalogueAdd', 'catalogueGet', 'catalogueList',
            'changeUserPassword', 'checkDiagVersion', 'checkOneTimeLogin', 'checkUser', 'checkUserOldPortal',
            'createOneTimeToken', 'ecuErrorCheck', 'ecuVariantBackup', 'ecuVariants', 'ecuVariantsCheck',
            'ecuVariantsGet', 'flashwareBulkInsert', 'germanserver_doneJob_MapCode', 'germanserver_doneJob_MapCode1',
            'germanserver_doneJob_MapCode2', 'germanserver_getJob_MapCode', 'getActiveTokens', 'headunitCheck',
            'hu5s1GetInfo', 'key_donejob', 'key_getjob', 'key_insertjob', 'key_listenjob', 'loginUser',
            'mapcodeAdd', 'mapcodeGet', 'mapcodetypeGet', 'registerUser', 'sacodeAdd', 'sacodeGet',
            'SeedKeyAdd', 'seedkeyGet', 'seedkeyList', 'sgktAdd', 'storeLogins', 'tokenAdd', 'tokenBuy',
            'tokenBuy_backup_v1', 'tokenCalc', 'tokenCheck', 'tokenCheck_backup_v1', 'tokenCheck_backup_v2',
            'tokenCheck_backup_v3', 'tokenCheck_test', 'tokenConfirm', 'tokenHistory', 'tokenTransaction',
            'tokenTypeList', 'tsk_changeState', 'tsk_downloadFlashware', 'tsk_extractFlashware',
            'tsk_updateFlashware', 'userTokenList', 'vedoc_doneJob_AntitheftCode', 'vedoc_doneJob_VINDecoder',
            'vedoc_getJob_AntitheftCode', 'vedoc_getJob_VINDecoder'
        ];
    }

    // Mevcut procedure'leri listele
    async listProcedures() {
        return new Promise((resolve, reject) => {
            const query = {
                query: "SHOW PROCEDURE STATUS WHERE Db = ?",
                params: ['xentry']
            };
            
            this.QueryExec(query, (result) => {
                if (result) {
                    resolve(result);
                } else {
                    reject("Procedure'ler listelenemedi");
                }
            });
        });
    }

    // Procedure detayını getir
    async getProcedureDetail(procedureName) {
        return new Promise((resolve, reject) => {
            const query = {
                query: "SHOW CREATE PROCEDURE ??",
                params: [procedureName]
            };
            
            this.QueryExec(query, (result) => {
                if (result && result.length > 0) {
                    resolve(result[0]);
                } else {
                    reject(`${procedureName} procedure detayı alınamadı`);
                }
            });
        });
    }

    // Procedure çalıştır
    async callProcedure(procedureName, parameters = []) {
        return new Promise((resolve, reject) => {
            const placeholders = parameters.length > 0 ? 
                '(' + new Array(parameters.length).fill('?').join(', ') + ')' : '()';
            
            const query = {
                query: `CALL ??${placeholders}`,
                params: [procedureName, ...parameters]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(result);
                } else {
                    reject(`${procedureName} procedure çalıştırılamadı`);
                }
            });
        });
    }

    // Yeni procedure oluştur
    async createProcedure(procedureName, parameters, body, options = {}) {
        return new Promise((resolve, reject) => {
            // Güvenlik kontrolü - mevcut procedure'leri koruma
            if (this.existingProcedures.includes(procedureName)) {
                reject(`UYARI: ${procedureName} mevcut bir procedure'dür. Güvenlik nedeniyle değiştirilemez.`);
                return;
            }

            const parameterList = parameters.map(param => {
                let paramDef = '';
                if (param.direction) paramDef += `${param.direction} `;
                paramDef += `${param.name} ${param.type}`;
                return paramDef;
            }).join(', ');

            const characteristics = [];
            if (options.deterministic) characteristics.push('DETERMINISTIC');
            if (options.readSqlData) characteristics.push('READS SQL DATA');
            if (options.modifiesSqlData) characteristics.push('MODIFIES SQL DATA');
            if (options.sqlSecurity) characteristics.push(`SQL SECURITY ${options.sqlSecurity}`);
            if (options.comment) characteristics.push(`COMMENT '${options.comment}'`);

            const characteristicsClause = characteristics.length > 0 ? 
                characteristics.join(' ') + ' ' : '';

            const procedureSQL = `
                CREATE PROCEDURE \`${procedureName}\`(${parameterList})
                ${characteristicsClause}
                BEGIN
                    ${body}
                END
            `;

            const query = {
                query: procedureSQL,
                params: []
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${procedureName} procedure başarıyla oluşturuldu`);
                } else {
                    reject(`${procedureName} procedure oluşturulamadı`);
                }
            });
        });
    }

    // Procedure sil (sadece yeni oluşturulanlar)
    async dropProcedure(procedureName) {
        return new Promise((resolve, reject) => {
            // Güvenlik kontrolü - mevcut procedure'leri koruma
            if (this.existingProcedures.includes(procedureName)) {
                reject(`UYARI: ${procedureName} sistem procedure'üdür. Güvenlik nedeniyle silinemez.`);
                return;
            }

            const query = {
                query: "DROP PROCEDURE IF EXISTS ??",
                params: [procedureName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${procedureName} procedure başarıyla silindi`);
                } else {
                    reject(`${procedureName} procedure silinemedi`);
                }
            });
        });
    }

    // Procedure'ü güncelle (sadece yeni oluşturulanlar)
    async updateProcedure(procedureName, parameters, body, options = {}) {
        try {
            // Önce sil, sonra yeniden oluştur
            await this.dropProcedure(procedureName);
            return await this.createProcedure(procedureName, parameters, body, options);
        } catch (error) {
            throw error;
        }
    }

    // Function oluştur
    async createFunction(functionName, parameters, returnType, body, options = {}) {
        return new Promise((resolve, reject) => {
            const parameterList = parameters.map(param => 
                `${param.name} ${param.type}`
            ).join(', ');

            const characteristics = [];
            if (options.deterministic) characteristics.push('DETERMINISTIC');
            if (options.readSqlData) characteristics.push('READS SQL DATA');
            if (options.sqlSecurity) characteristics.push(`SQL SECURITY ${options.sqlSecurity}`);
            if (options.comment) characteristics.push(`COMMENT '${options.comment}'`);

            const characteristicsClause = characteristics.length > 0 ? 
                characteristics.join(' ') + ' ' : '';

            const functionSQL = `
                CREATE FUNCTION \`${functionName}\`(${parameterList})
                RETURNS ${returnType}
                ${characteristicsClause}
                BEGIN
                    ${body}
                END
            `;

            const query = {
                query: functionSQL,
                params: []
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${functionName} function başarıyla oluşturuldu`);
                } else {
                    reject(`${functionName} function oluşturulamadı`);
                }
            });
        });
    }

    // Function sil
    async dropFunction(functionName) {
        return new Promise((resolve, reject) => {
            const query = {
                query: "DROP FUNCTION IF EXISTS ??",
                params: [functionName]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(`${functionName} function başarıyla silindi`);
                } else {
                    reject(`${functionName} function silinemedi`);
                }
            });
        });
    }

    // Function'ları listele
    async listFunctions() {
        return new Promise((resolve, reject) => {
            const query = {
                query: "SHOW FUNCTION STATUS WHERE Db = ?",
                params: ['xentry']
            };
            
            this.QueryExec(query, (result) => {
                if (result) {
                    resolve(result);
                } else {
                    reject("Function'lar listelenemedi");
                }
            });
        });
    }

    // Procedure/Function'ın var olup olmadığını kontrol et
    async exists(name, type = 'PROCEDURE') {
        return new Promise((resolve, reject) => {
            const query = {
                query: `SHOW ${type} STATUS WHERE Db = ? AND Name = ?`,
                params: ['xentry', name]
            };
            
            this.QueryExec(query, (result) => {
                if (result !== null) {
                    resolve(result.length > 0);
                } else {
                    reject(`${name} ${type.toLowerCase()} kontrol edilemedi`);
                }
            });
        });
    }

    // Güvenli procedure oluşturma (mevcut olanları kontrol eder)
    async safeCreateProcedure(procedureName, parameters, body, options = {}) {
        try {
            const exists = await this.exists(procedureName, 'PROCEDURE');
            if (exists && this.existingProcedures.includes(procedureName)) {
                throw new Error(`UYARI: ${procedureName} sistem procedure'üdür. Değiştirilemez.`);
            }
            
            if (exists) {
                console.log(`${procedureName} zaten mevcut. Güncelleniyor...`);
                return await this.updateProcedure(procedureName, parameters, body, options);
            } else {
                return await this.createProcedure(procedureName, parameters, body, options);
            }
        } catch (error) {
            throw error;
        }
    }
}

export default ProcedureManager;
