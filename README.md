# Mblink Database Service

Mblink Database erişimi ve MySQL procedure yönetimi için geliştirilmiş Node.js servisi.

## 🚀 Özellikler

- **Database Manager**: Veritabanı tablolarını yönetme (CRUD işlemleri)
- **Procedure Manager**: MySQL stored procedure ve function yönetimi
- **Güvenlik**: Mevcut sistem procedure'le<PERSON> koruma
- **Environment Variables**: Güvenli konfigürasyon yönetimi
- **Async/Await**: Modern JavaScript desteği

## 📋 Gereksinimler

- Node.js >= 14.0.0
- MySQL Database
- npm veya yarn

## 🛠️ <PERSON><PERSON>lum

```bash
# Repository'yi klonlayın
git clone <repository-url>
cd database-service

# Bağımlılıkları yükleyin
npm install

# Environment dosyasını kopyalayın
cp .env.example .env

# .env dosyasını düzenleyin
nano .env
```

## ⚙️ Konfigürasyon

`.env` dosyasında aşağıdaki değişkenleri ayarlayın:

```env
# Database Configuration
DB_HOST=your_database_host
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
DB_CONNECTION_LIMIT=100
DB_MULTIPLE_STATEMENTS=true
DB_DEBUG=false

# Application Configuration
NODE_ENV=development
PORT=3000
```

## 🎯 Kullanım

### Temel Kullanım

```javascript
import MblinkDatabaseService from './index.js';

const service = new MblinkDatabaseService();
const dbManager = service.getDbManager();
const procManager = service.getProcManager();

// Servis durumunu kontrol et
const status = await service.checkStatus();
console.log(status);
```

### Database Manager

```javascript
// Tablo bilgisi alma
const tableInfo = await dbManager.getTableInfo('Users');

// Veri sorgulama
const users = await dbManager.selectData('Users', {
    where: { active: 1 },
    limit: 10,
    orderBy: 'created_at DESC'
});

// Veri ekleme
const result = await dbManager.insertData('Users', {
    username: 'testuser',
    email: '<EMAIL>'
});

// Veri güncelleme
await dbManager.updateData('Users', 
    { email: '<EMAIL>' },
    { id: 1 }
);

// Veri silme
await dbManager.deleteData('Users', { id: 1 });
```

### Procedure Manager

```javascript
// Mevcut procedure'leri listele
const procedures = await procManager.listProcedures();

// Procedure çalıştır
const result = await procManager.callProcedure('tokenTypeList');

// Yeni procedure oluştur
await procManager.safeCreateProcedure(
    'myCustomProcedure',
    [
        { name: 'userId', type: 'INT', direction: 'IN' },
        { name: 'result', type: 'VARCHAR(255)', direction: 'OUT' }
    ],
    `
        DECLARE userName VARCHAR(255);
        SELECT username INTO userName FROM Users WHERE id = userId;
        SET result = CONCAT('Hello, ', userName);
    `,
    {
        comment: 'Kullanıcı selamlama procedure\'ü',
        modifiesSqlData: true
    }
);

// Function oluştur
await procManager.createFunction(
    'calculateAge',
    [{ name: 'birthDate', type: 'DATE' }],
    'INT',
    'RETURN YEAR(CURDATE()) - YEAR(birthDate);',
    {
        comment: 'Yaş hesaplama function\'ı',
        deterministic: true
    }
);
```

## 🔒 Güvenlik

Bu servis canlı sistemlerde güvenli kullanım için tasarlanmıştır:

- **Mevcut Procedure Koruması**: Sistem procedure'leri değiştirilemez veya silinemez
- **Parameterized Queries**: SQL injection koruması
- **Environment Variables**: Hassas bilgilerin güvenli saklanması
- **Error Handling**: Detaylı hata yönetimi

### Korunan Procedure'ler

Aşağıdaki sistem procedure'leri korunmaktadır:
- `tokenCheck`, `tokenBuy`, `loginUser`, `registerUser`
- `antitheftcodeAdd`, `mapcodeGet`, `seedkeyGet`
- Ve diğer tüm mevcut sistem procedure'leri

## 📁 Proje Yapısı

```
database-service/
├── src/
│   ├── dbconnector.js      # Temel veritabanı bağlantısı
│   ├── DatabaseManager.js  # Veritabanı yönetimi
│   └── ProcedureManager.js  # Procedure yönetimi
├── examples/
│   └── usage-examples.js    # Kullanım örnekleri
├── tests/
│   └── (test dosyaları)
├── config/
│   └── (konfigürasyon dosyaları)
├── index.js                # Ana giriş noktası
├── test-service.js         # Test scripti
├── inspect-database.js     # Veritabanı inceleme scripti
├── package.json
├── .env.example
└── README.md
```

## 🧪 Test

```bash
# Temel test
npm test

# Servis testi
node test-service.js

# Kullanım örnekleri
node examples/usage-examples.js

# Veritabanı inceleme
node inspect-database.js
```

## 📊 Mevcut Veritabanı Yapısı

Sistem şu anda **37 tablo** ve **62 stored procedure** içermektedir:

### Ana Kategoriler:
- **Kullanıcı Yönetimi**: Users, UserLogins, UserSubs, UserTokens
- **Token Sistemi**: TokenBuy, TokenPrice, TokenSpent, TokenTypes
- **ECU Yönetimi**: ECU_Names, ECU_Errors, ECU_DiagCodes
- **Map Kodları**: MapCodes, MapCodeTypes, UserMapCodes
- **Güvenlik**: AntiTheftCodes, SeedKeys, VINSaCodes
- **Flashware**: Flashwares, Catalogue

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 👨‍💻 Geliştirici

**Reha Biçer** - Mblink Database Service

## ⚠️ Önemli Notlar

- Bu servis canlı bir sistemde çalışmaktadır
- Değişiklikler yapmadan önce mutlaka test edin
- Sistem procedure'lerini değiştirmeyin
- Backup almayı unutmayın

## 📞 Destek

Herhangi bir sorun veya soru için lütfen issue oluşturun.
