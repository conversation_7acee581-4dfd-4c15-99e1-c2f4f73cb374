{"name": "mblink-database-service", "version": "1.0.0", "description": "Mblink Database Management and Procedure Creation Service", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["mysql", "database", "procedures", "mblink"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"mysql": "^2.18.1", "async": "^3.2.4", "dotenv": "^16.3.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}