"use strict"
/*************************
    Async DB Connector
    Coded by <PERSON><PERSON>
**************************/

import mysql from "mysql";
import async from "async";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

class DBConnector {
    constructor () {
        this.pool = mysql.createPool({
            connectionLimit : parseInt(process.env.DB_CONNECTION_LIMIT) || 100,
            host     : process.env.DB_HOST || '*************',
            user     : process.env.DB_USER || 'xentry',
            password : process.env.DB_PASSWORD || 'Xy2020Xp!',
            database : process.env.DB_NAME || 'xentry',
            multipleStatements: process.env.DB_MULTIPLE_STATEMENTS === 'true' || true,
            debug    : process.env.DB_DEBUG === 'true' || false
        });
    }

    QueryExec(SQLquery,callback) {
        const self = this;

        async.waterfall([
                function(callback) {
                    self.pool.getConnection(function(err,connection){
                    if(err) {
                        callback(true);
                    } else {
                        callback(null,connection);
                    }
                    });
                },
                function(connection,callback) {
                    callback(null,connection,SQLquery);
                },
                function(connection,SQLquery,callback) {
                    try {
                        connection.query(SQLquery.query, SQLquery.params ,function(err,rows){                        
                            connection.release();
                            if(!err) {   
                                callback((typeof(rows.length)==='undefined' || rows.length === 0) && rows.affectedRows === 0 ? false : rows);
                            } else {
                                callback(true);
                            }
                        });    
                    } catch (e) {
                        callback(false);
                    }
                }
            ],
            function(result){
                if(typeof(result) === "boolean" && result === true) {
                    callback(null);
                } else {
                    callback(result);
                }
            }
        );
    }    
    
}
export default DBConnector;
