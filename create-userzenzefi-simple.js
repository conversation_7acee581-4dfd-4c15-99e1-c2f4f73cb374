import MblinkDatabaseService from './index.js';

async function createUserZenzefiCalcTable() {
    console.log("🔍 UserZenzefiCalc tablosu oluşturuluyor...\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    
    try {
        // Önce UserMapCodes tablosunun yapısını inceleyelim
        console.log("1. UserMapCodes tablosu yapısı inceleniyor...");
        const userMapCodesStructure = await dbManager.getTableInfo('UserMapCodes');
        console.log("UserMapCodes yapısı:");
        userMapCodesStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n2. UserZenzefiCalc tablosu oluşturuluyor (SQL ile)...");
        
        // Direkt SQL ile tablo oluştur
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS UserZenzefiCalc (
                id INT(11) NOT NULL AUTO_INCREMENT,
                userid INT(11) NOT NULL,
                remains INT(11) NOT NULL DEFAULT 0,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                INDEX idx_userzenzefi_userid (userid),
                INDEX idx_userzenzefi_userid_remains (userid, remains),
                INDEX idx_userzenzefi_created_at (created_at),
                CONSTRAINT fk_userzenzefi_userid 
                    FOREIGN KEY (userid) REFERENCES Users(id) 
                    ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `;
        
        await new Promise((resolve, reject) => {
            dbManager.QueryExec({
                query: createTableSQL,
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ UserZenzefiCalc tablosu başarıyla oluşturuldu");
                    resolve(result);
                } else {
                    reject("Tablo oluşturulamadı");
                }
            });
        });
        
        console.log("\n3. Tablo yapısı kontrol ediliyor...");
        const newTableStructure = await dbManager.getTableInfo('UserZenzefiCalc');
        console.log("UserZenzefiCalc tablo yapısı:");
        newTableStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n4. Index'ler kontrol ediliyor...");
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "SHOW INDEX FROM UserZenzefiCalc",
                params: []
            }, (result) => {
                if (result) {
                    console.log("UserZenzefiCalc index'leri:");
                    result.forEach(index => {
                        console.log(`   ${index.Key_name} | ${index.Column_name} | ${index.Index_type} | ${index.Non_unique ? 'NON-UNIQUE' : 'UNIQUE'}`);
                    });
                }
                resolve(result);
            });
        });
        
        console.log("\n5. Test verisi ekleniyor...");
        
        // Test için örnek veri ekle
        try {
            const testData = {
                userid: 1,
                remains: 10
            };
            
            const insertResult = await dbManager.insertData('UserZenzefiCalc', testData);
            console.log("✅ Test verisi eklendi:", insertResult);
            
            // Test verisini sorgula
            const testQuery = await dbManager.selectData('UserZenzefiCalc', {
                where: { userid: 1 },
                limit: 1
            });
            console.log("📊 Test verisi sorgulandı:", testQuery);
            
        } catch (error) {
            console.log("⚠️  Test verisi eklenemedi:", error.message);
        }
        
        console.log("\n🎉 UserZenzefiCalc tablosu başarıyla oluşturuldu!");
        console.log("\n📋 Tablo Özellikleri:");
        console.log("   - Primary Key: id (AUTO_INCREMENT)");
        console.log("   - Foreign Key: userid -> Users.id (CASCADE)");
        console.log("   - Index'ler: userid, userid+remains, created_at");
        console.log("   - Timestamp'ler: created_at, updated_at (otomatik)");
        console.log("   - Engine: InnoDB");
        console.log("   - Charset: utf8mb4_unicode_ci");
        
    } catch (error) {
        console.error("❌ Tablo oluşturma hatası:", error);
    }
}

// Scripti çalıştır
createUserZenzefiCalcTable();
