import MblinkDatabaseService from './index.js';

async function checkZenzefiCalcLogsTable() {
    console.log("🔍 ZenzefiCalcLogs tablosu kontrol ediliyor...\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    
    try {
        console.log("1. ZenzefiCalcLogs tablo yapısı:");
        const tableStructure = await dbManager.getTableInfo('ZenzefiCalcLogs');
        tableStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n2. Index'ler kontrol ediliyor:");
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "SHOW INDEX FROM ZenzefiCalcLogs",
                params: []
            }, (result) => {
                if (result) {
                    console.log("ZenzefiCalcLogs index'leri:");
                    result.forEach(index => {
                        console.log(`   ${index.Key_name} | ${index.Column_name} | ${index.Index_type} | ${index.Non_unique ? 'NON-UNIQUE' : 'UNIQUE'}`);
                    });
                }
                resolve(result);
            });
        });
        
        console.log("\n3. Test verisi ekleniyor:");
        try {
            // Test verisi ekle
            const testData = {
                userid: 1,
                user_hex_data: '48656C6C6F20576F726C64', // "Hello World" in HEX
                status: 'pending',
                server_ip: '127.0.0.1',
                user_agent: 'MblinkTestClient/1.0',
                session_id: 'test_session_' + Date.now()
            };
            
            const insertResult = await dbManager.insertData('ZenzefiCalcLogs', testData);
            console.log("✅ Test verisi eklendi:", insertResult);
            
            // Status güncelleme
            const updateResult = await dbManager.updateData('ZenzefiCalcLogs', 
                { 
                    status: 'completed',
                    zenzefi_hex_result: '576F726C6420486F6C6C65', // "World Holle" in HEX
                    response_timestamp: new Date(),
                    processing_time_ms: 1250
                },
                { id: insertResult.insertId }
            );
            console.log("✅ Test verisi güncellendi:", updateResult);
            
            // Test verisini sorgula
            const testQuery = await dbManager.selectData('ZenzefiCalcLogs', {
                where: { id: insertResult.insertId },
                limit: 1
            });
            
            if (testQuery.length > 0) {
                const record = testQuery[0];
                console.log("📊 Test verisi:");
                console.log(`   ID: ${record.id}`);
                console.log(`   UserID: ${record.userid}`);
                console.log(`   User HEX: ${record.user_hex_data}`);
                console.log(`   ZenzefiServer HEX: ${record.zenzefi_hex_result}`);
                console.log(`   Status: ${record.status}`);
                console.log(`   Created: ${record.created_at}`);
                console.log(`   Updated: ${record.updated_at}`);
                console.log(`   Processing Time: ${record.processing_time_ms}ms`);
                console.log(`   Session ID: ${record.session_id}`);
            }
            
        } catch (error) {
            console.log("⚠️  Test verisi hatası:", error.message);
        }
        
        console.log("\n4. Status değerleri testi:");
        const statusValues = ['pending', 'processing', 'completed', 'failed', 'timeout'];
        
        for (const status of statusValues) {
            try {
                const testData = {
                    userid: 1,
                    user_hex_data: `48656C6C6F${status}`, // Test HEX
                    status: status,
                    server_ip: '***********',
                    session_id: `${status}_test_${Date.now()}`
                };
                
                const result = await dbManager.insertData('ZenzefiCalcLogs', testData);
                console.log(`✅ ${status} status testi başarılı (ID: ${result.insertId})`);
                
            } catch (error) {
                console.log(`❌ ${status} status testi başarısız: ${error.message}`);
            }
        }
        
        console.log("\n5. Son kayıtları görüntüleme:");
        const recentLogs = await dbManager.selectData('ZenzefiCalcLogs', {
            limit: 10,
            orderBy: 'created_at DESC'
        });
        
        console.log("📊 Son 10 kayıt:");
        recentLogs.forEach((record, index) => {
            console.log(`   ${index + 1}. ID: ${record.id}, UserID: ${record.userid}, Status: ${record.status}, Created: ${record.created_at}`);
        });
        
        console.log("\n6. Status istatistikleri:");
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "SELECT status, COUNT(*) as count FROM ZenzefiCalcLogs GROUP BY status ORDER BY count DESC",
                params: []
            }, (result) => {
                if (result) {
                    console.log("Status dağılımı:");
                    result.forEach(stat => {
                        console.log(`   ${stat.status}: ${stat.count} kayıt`);
                    });
                }
                resolve(result);
            });
        });
        
        console.log("\n🎉 ZenzefiCalcLogs tablosu başarıyla test edildi!");
        console.log("\n📋 Tablo Özeti:");
        console.log("   ✅ Tablo yapısı: Backend server verileri için optimize edildi");
        console.log("   ✅ userid kolonu: Users.id referansı");
        console.log("   ✅ user_hex_data: Kullanıcı HEX verileri (TEXT)");
        console.log("   ✅ zenzefi_hex_result: ZenzefiServer HEX sonuçları (TEXT)");
        console.log("   ✅ status: İşlem durumu (ENUM)");
        console.log("   ✅ created_at: Kayıt oluşturulma zamanı");
        console.log("   ✅ Timestamp tracking: request/response zamanları");
        console.log("   ✅ Performance monitoring: processing_time_ms");
        console.log("   ✅ Session tracking: server_ip, user_agent, session_id");
        console.log("   ✅ Error handling: error_message kolonu");
        
    } catch (error) {
        console.error("❌ Tablo kontrol hatası:", error);
    }
}

// Scripti çalıştır
checkZenzefiCalcLogsTable();
