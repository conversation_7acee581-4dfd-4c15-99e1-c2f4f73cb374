import MblinkDatabaseService from './index.js';

async function testService() {
    console.log("🚀 Mblink Database Service Test Başlatılıyor...\n");
    
    const service = new MblinkDatabaseService();
    
    try {
        // Servis durumunu kontrol et
        console.log("1. Servis durumu kontrol ediliyor...");
        const status = await service.checkStatus();
        console.log("   Status:", status);
        console.log("");

        // Güvenli testleri çalıştır
        console.log("2. Güvenli testler çalıştırılıyor...");
        const testResult = await service.runSafeTests();
        console.log("   Test sonucu:", testResult ? "✅ Başarılı" : "❌ Başarısız");
        console.log("");

        // Database Manager testi
        console.log("3. Database Manager test ediliyor...");
        const dbManager = service.getDbManager();
        const tables = await dbManager.getAllTables();
        console.log(`   ✅ ${tables.length} tablo bulundu`);
        console.log("");

        // Procedure Manager testi
        console.log("4. Procedure Manager test ediliyor...");
        const procManager = service.getProcManager();
        const procedures = await procManager.listProcedures();
        console.log(`   ✅ ${procedures.length} procedure bulundu`);
        console.log("");

        console.log("🎉 Tüm testler başarıyla tamamlandı!");

    } catch (error) {
        console.error("❌ Test sırasında hata:", error);
    }
}

testService();
