"use strict"
/*************************
    Mblink Database Service
    Main Entry Point
    Coded by <PERSON><PERSON>
**************************/

import DatabaseManager from './src/DatabaseManager.js';
import ProcedureManager from './src/ProcedureManager.js';
import UserZenzefiManager from './src/UserZenzefiManager.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

class MblinkDatabaseService {
    constructor() {
        this.dbManager = new DatabaseManager();
        this.procManager = new ProcedureManager();
        this.zenzefiManager = new UserZenzefiManager();
    }

    // Database Manager'ı döndür
    getDbManager() {
        return this.dbManager;
    }

    // Procedure Manager'ı döndür
    getProcManager() {
        return this.procManager;
    }

    // UserZenzefi Manager'ı döndür
    getZenzefiManager() {
        return this.zenzefiManager;
    }

    // Servis durumunu kontrol et
    async checkStatus() {
        try {
            const tables = await this.dbManager.getAllTables();
            const procedures = await this.procManager.listProcedures();
            
            return {
                status: 'OK',
                database: process.env.DB_NAME || 'xentry',
                tablesCount: tables.length,
                proceduresCount: procedures.length,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                status: 'ERROR',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // Güvenli test işlemleri
    async runSafeTests() {
        console.log("🔍 Mblink Database Service - Güvenli Test Başlatılıyor...\n");
        
        try {
            // 1. Bağlantı testi
            console.log("1. Veritabanı bağlantısı test ediliyor...");
            const status = await this.checkStatus();
            console.log(`   ✅ Bağlantı başarılı: ${status.tablesCount} tablo, ${status.proceduresCount} procedure\n`);

            // 2. Tablo listesi
            console.log("2. Mevcut tablolar kontrol ediliyor...");
            const tables = await this.dbManager.getAllTables();
            console.log(`   ✅ ${tables.length} tablo bulundu\n`);

            // 3. Procedure listesi
            console.log("3. Mevcut procedure'ler kontrol ediliyor...");
            const procedures = await this.procManager.listProcedures();
            console.log(`   ✅ ${procedures.length} procedure bulundu\n`);

            // 4. Örnek procedure çağrısı (güvenli)
            console.log("4. Örnek procedure testi (tokenTypeList)...");
            try {
                const tokenTypes = await this.procManager.callProcedure('tokenTypeList');
                console.log(`   ✅ tokenTypeList procedure başarıyla çalıştırıldı\n`);
            } catch (error) {
                console.log(`   ⚠️  tokenTypeList procedure test edilemedi: ${error.message}\n`);
            }

            console.log("🎉 Tüm testler tamamlandı!");
            return true;

        } catch (error) {
            console.error("❌ Test sırasında hata:", error.message);
            return false;
        }
    }

    // Örnek yeni procedure oluşturma
    async createSampleProcedure() {
        try {
            console.log("📝 Örnek procedure oluşturuluyor...");
            
            const procedureName = 'test_getUserCount';
            const parameters = [];
            const body = `
                DECLARE userCount INT DEFAULT 0;
                SELECT COUNT(*) INTO userCount FROM Users;
                SELECT userCount as total_users;
            `;
            const options = {
                comment: 'Test procedure - kullanıcı sayısını döndürür',
                readSqlData: true
            };

            const result = await this.procManager.safeCreateProcedure(
                procedureName, 
                parameters, 
                body, 
                options
            );
            
            console.log(`✅ ${result}`);
            
            // Test et
            const testResult = await this.procManager.callProcedure(procedureName);
            console.log("📊 Test sonucu:", testResult);
            
            return true;
        } catch (error) {
            console.error("❌ Örnek procedure oluşturma hatası:", error.message);
            return false;
        }
    }

    // Örnek function oluşturma
    async createSampleFunction() {
        try {
            console.log("🔧 Örnek function oluşturuluyor...");
            
            const functionName = 'test_calculateTokenValue';
            const parameters = [
                { name: 'tokenAmount', type: 'INT' },
                { name: 'tokenTypeId', type: 'INT' }
            ];
            const returnType = 'DECIMAL(10,2)';
            const body = `
                DECLARE price DECIMAL(10,2) DEFAULT 0.0;
                SELECT tokenPrice INTO price FROM TokenPrice WHERE tokenType = tokenTypeId LIMIT 1;
                RETURN tokenAmount * price;
            `;
            const options = {
                comment: 'Test function - token değerini hesaplar',
                readSqlData: true,
                deterministic: true
            };

            const result = await this.procManager.createFunction(
                functionName, 
                parameters, 
                returnType, 
                body, 
                options
            );
            
            console.log(`✅ ${result}`);
            return true;
        } catch (error) {
            console.error("❌ Örnek function oluşturma hatası:", error.message);
            return false;
        }
    }
}

// Ana çalıştırma
async function main() {
    const service = new MblinkDatabaseService();
    
    console.log("🚀 Mblink Database Service Başlatılıyor...\n");
    
    // Güvenli testleri çalıştır
    await service.runSafeTests();
    
    console.log("\n" + "=".repeat(50));
    console.log("📚 Kullanım Örnekleri:");
    console.log("=".repeat(50));
    
    console.log(`
// Database Manager kullanımı:
const dbManager = service.getDbManager();
const users = await dbManager.selectData('Users', { limit: 10 });

// Procedure Manager kullanımı:
const procManager = service.getProcManager();
const result = await procManager.callProcedure('tokenTypeList');

// Yeni procedure oluşturma:
await procManager.safeCreateProcedure('myProcedure', parameters, body, options);
    `);
}

// Export for module usage
export default MblinkDatabaseService;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
