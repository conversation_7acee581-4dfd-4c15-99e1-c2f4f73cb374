import MblinkDatabaseService from './index.js';

async function createZenzefiCalcLogsTable() {
    console.log("🔍 ZenzefiCalcLogs tablosu oluşturuluyor...\n");
    
    const service = new MblinkDatabaseService();
    const dbManager = service.getDbManager();
    
    try {
        console.log("1. Mevcut tablo yapıları referans alınıyor...");
        
        // UserZenzefiCalc tablosunu referans al
        const userZenzefiStructure = await dbManager.getTableInfo('UserZenzefiCalc');
        console.log("UserZenzefiCalc yapısı (referans):");
        userZenzefiStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key}`);
        });
        
        console.log("\n2. ZenzefiCalcLogs tablosu oluşturuluyor...");
        
        // ZenzefiCalcLogs tablosu için SQL
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS ZenzefiCalcLogs (
                id INT(11) NOT NULL AUTO_INCREMENT,
                userid INT(11) NOT NULL,
                user_hex_data TEXT NULL COMMENT 'Kullanıcı tarafından gönderilen HEX değerleri',
                zenzefi_hex_result TEXT NULL COMMENT 'ZenzefiServer tarafından hesaplanmış HEX değerleri',
                status ENUM('pending', 'processing', 'completed', 'failed', 'timeout') NOT NULL DEFAULT 'pending' COMMENT 'İşlem durumu',
                error_message TEXT NULL COMMENT 'Hata mesajı (varsa)',
                request_timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'İstek zamanı',
                response_timestamp DATETIME NULL COMMENT 'Yanıt zamanı',
                processing_time_ms INT(11) NULL COMMENT 'İşlem süresi (milisaniye)',
                server_ip VARCHAR(45) NULL COMMENT 'İstek yapılan server IP',
                user_agent TEXT NULL COMMENT 'Kullanıcı agent bilgisi',
                session_id VARCHAR(255) NULL COMMENT 'Oturum ID',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Kayıt oluşturulma zamanı',
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Son güncelleme zamanı',
                PRIMARY KEY (id),
                INDEX idx_zenzefi_logs_userid (userid),
                INDEX idx_zenzefi_logs_status (status),
                INDEX idx_zenzefi_logs_created_at (created_at),
                INDEX idx_zenzefi_logs_request_timestamp (request_timestamp),
                INDEX idx_zenzefi_logs_userid_status (userid, status),
                INDEX idx_zenzefi_logs_status_created (status, created_at),
                INDEX idx_zenzefi_logs_userid_created (userid, created_at),
                CONSTRAINT fk_zenzefi_logs_userid
                    FOREIGN KEY (userid) REFERENCES Users(id)
                    ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='ZenzefiCalc işlem logları - Backend server verileri'
        `;
        
        await new Promise((resolve, reject) => {
            dbManager.QueryExec({
                query: createTableSQL,
                params: []
            }, (result) => {
                if (result !== null) {
                    console.log("✅ ZenzefiCalcLogs tablosu başarıyla oluşturuldu");
                    resolve(result);
                } else {
                    reject("Tablo oluşturulamadı");
                }
            });
        });
        
        console.log("\n3. Tablo yapısı kontrol ediliyor...");
        const newTableStructure = await dbManager.getTableInfo('ZenzefiCalcLogs');
        console.log("ZenzefiCalcLogs tablo yapısı:");
        newTableStructure.forEach(field => {
            console.log(`   ${field.Field} | ${field.Type} | ${field.Null} | ${field.Key} | ${field.Default} | ${field.Extra}`);
        });
        
        console.log("\n4. Index'ler kontrol ediliyor...");
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: "SHOW INDEX FROM ZenzefiCalcLogs",
                params: []
            }, (result) => {
                if (result) {
                    console.log("ZenzefiCalcLogs index'leri:");
                    result.forEach(index => {
                        console.log(`   ${index.Key_name} | ${index.Column_name} | ${index.Index_type} | ${index.Non_unique ? 'NON-UNIQUE' : 'UNIQUE'}`);
                    });
                }
                resolve(result);
            });
        });
        
        console.log("\n5. Foreign Key constraints kontrol ediliyor...");
        await new Promise((resolve) => {
            dbManager.QueryExec({
                query: `SELECT 
                    CONSTRAINT_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME,
                    DELETE_RULE,
                    UPDATE_RULE
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'ZenzefiCalcLogs' 
                AND REFERENCED_TABLE_NAME IS NOT NULL`,
                params: []
            }, (result) => {
                if (result && result.length > 0) {
                    console.log("Foreign Key constraints:");
                    result.forEach(fk => {
                        console.log(`   ${fk.CONSTRAINT_NAME}: ${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME} (DELETE: ${fk.DELETE_RULE}, UPDATE: ${fk.UPDATE_RULE})`);
                    });
                } else {
                    console.log("   Foreign Key constraint bulunamadı");
                }
                resolve(result);
            });
        });
        
        console.log("\n6. Test verisi ekleniyor...");
        
        try {
            // Test verisi ekle
            const testData = {
                userid: 1,
                user_hex_data: '48656C6C6F20576F726C64', // "Hello World" in HEX
                status: 'pending',
                server_ip: '127.0.0.1',
                user_agent: 'MblinkTestClient/1.0',
                session_id: 'test_session_' + Date.now()
            };
            
            const insertResult = await dbManager.insertData('ZenzefiCalcLogs', testData);
            console.log("✅ Test verisi eklendi:", insertResult);
            
            // Test verisini sorgula
            const testQuery = await dbManager.selectData('ZenzefiCalcLogs', {
                limit: 1,
                orderBy: 'id DESC'
            });
            console.log("📊 Test verisi:");
            if (testQuery.length > 0) {
                const record = testQuery[0];
                console.log(`   ID: ${record.id}`);
                console.log(`   UserID: ${record.userid}`);
                console.log(`   User HEX: ${record.user_hex_data}`);
                console.log(`   Status: ${record.status}`);
                console.log(`   Request Time: ${record.request_timestamp}`);
                console.log(`   Session ID: ${record.session_id}`);
            }
            
            // Status güncelleme testi
            console.log("\n7. Status güncelleme testi...");
            const updateResult = await dbManager.updateData('ZenzefiCalcLogs', 
                { 
                    status: 'completed',
                    zenzefi_hex_result: '576F726C6420486F6C6C65', // "World Holle" in HEX (reversed)
                    response_timestamp: new Date(),
                    processing_time_ms: 1250
                },
                { id: insertResult.insertId }
            );
            console.log("✅ Status güncellendi:", updateResult);
            
            // Güncellenmiş veriyi sorgula
            const updatedQuery = await dbManager.selectData('ZenzefiCalcLogs', {
                where: { id: insertResult.insertId },
                limit: 1
            });
            console.log("📊 Güncellenmiş veri:");
            if (updatedQuery.length > 0) {
                const record = updatedQuery[0];
                console.log(`   Status: ${record.status}`);
                console.log(`   ZenzefiServer HEX: ${record.zenzefi_hex_result}`);
                console.log(`   Processing Time: ${record.processing_time_ms}ms`);
                console.log(`   Response Time: ${record.response_timestamp}`);
            }
            
        } catch (error) {
            console.log("⚠️  Test verisi işlemi:", error.message);
        }
        
        console.log("\n🎉 ZenzefiCalcLogs tablosu başarıyla oluşturuldu!");
        console.log("\n📋 Tablo Özellikleri:");
        console.log("   ✅ Primary Key: id (AUTO_INCREMENT)");
        console.log("   ✅ Foreign Key: userid -> Users.id (CASCADE)");
        console.log("   ✅ user_hex_data: Kullanıcı HEX verileri (TEXT)");
        console.log("   ✅ zenzefi_hex_result: ZenzefiServer HEX sonuçları (TEXT)");
        console.log("   ✅ status: İşlem durumu (ENUM)");
        console.log("   ✅ created_at: Kayıt oluşturulma zamanı (otomatik)");
        console.log("   ✅ updated_at: Son güncelleme zamanı (otomatik)");
        console.log("   ✅ Timestamp'ler: request_timestamp, response_timestamp");
        console.log("   ✅ Performance: processing_time_ms");
        console.log("   ✅ Tracking: server_ip, user_agent, session_id");
        console.log("   ✅ Error Handling: error_message");
        console.log("   ✅ Index'ler: userid, status, created_at kombinasyonları");
        
        console.log("\n📊 Status Değerleri:");
        console.log("   🔄 pending: İstek alındı, işleme alınmadı");
        console.log("   ⚙️  processing: ZenzefiServer'da işleniyor");
        console.log("   ✅ completed: Başarıyla tamamlandı");
        console.log("   ❌ failed: İşlem başarısız");
        console.log("   ⏰ timeout: Zaman aşımı");
        
    } catch (error) {
        console.error("❌ Tablo oluşturma hatası:", error);
    }
}

// Scripti çalıştır
createZenzefiCalcLogsTable();
